import request from '@/utils/request'

/**
 * 获取返点规则
 */
export function getRebateRules() {
  return request({
    url: '/admin/rebate/rules',
    method: 'get'
  })
}

/**
 * 更新返点规则
 * @param {object} data - 规则数据
 */
export function updateRebateRules(data) {
  return request({
    url: '/admin/rebate/rules',
    method: 'put',
    data
  })
}

/**
 * 获取返点统计
 */
export function getRebateStats() {
  return request({
    url: '/admin/rebate/stats',
    method: 'get'
  })
}

/**
 * 获取返点记录
 * @param {object} params - 查询参数
 */
export function getRebateRecords(params) {
  return request({
    url: '/admin/rebate/records',
    method: 'get',
    params
  })
}

/**
 * 结算返点
 * @param {number} id - 返点记录ID
 */
export function settleRebate(id) {
  return request({
    url: `/admin/rebate/${id}/settle`,
    method: 'put'
  })
}

/**
 * 导出返点记录
 * @param {object} params - 查询参数
 */
export function exportRebateRecords(params) {
  return request({
    url: '/admin/rebate/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取用户关系费率信息
 * @param {string} account - 用户账号
 */
export function getUserRelationFee(account) {
  return request({
    url: `/api/admin/user-relation-fee/${account}`,
    method: 'get'
  })
}

/**
 * 获取用户详情
 * @param {string} account - 用户账号
 */
export function getUserDetails(account) {
  return request({
    url: `/api/admin/users/${account}`,
    method: 'get'
  })
}

/**
 * 获取所有平台手续费率
 */
export function getPlatformFeeRate() {
  return request({
    url: `/api/platform-fees`,
    method: 'get'
  })
}

/**
 * 账户充值
 * @param {string} accountId - 账户ID
 * @param {object} data - 充值数据
 */
export function depositToAccount(accountId, data) {
  return request({
    url: `/api/accounts/account/${accountId}/deposit`,
    method: 'post',
    params: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 返点计算和分配
 * @param {object} data - 返点计算参数
 * @param {number} data.category - 贵金属类型：0=黄金, 1=铂金, 2=钯金
 * @param {string} data.userId - 用户ID
 * @param {number} data.weight - 克重
 */
export function calculateAndDistributeRebate(data) {
  return request({
    url: '/api/rebate-calculation/calculate-and-distribute',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
